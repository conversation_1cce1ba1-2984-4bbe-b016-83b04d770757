{"name": "@astrojs/rss", "description": "Add RSS feeds to your Astro projects", "version": "4.0.12", "type": "module", "types": "./dist/index.d.ts", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/withastro/astro.git", "directory": "packages/astro-rss"}, "bugs": "https://github.com/withastro/astro/issues", "homepage": "https://astro.build", "exports": {".": "./dist/index.js", "./package.json": "./package.json"}, "files": ["dist"], "devDependencies": {"@types/xml2js": "^0.4.14", "xml2js": "0.6.2", "astro": "5.8.2", "astro-scripts": "0.0.14"}, "dependencies": {"fast-xml-parser": "^5.2.0", "kleur": "^4.1.5"}, "scripts": {"build": "astro-scripts build \"src/**/*.ts\" && tsc", "build:ci": "astro-scripts build \"src/**/*.ts\"", "dev": "astro-scripts dev \"src/**/*.ts\"", "test": "astro-scripts test \"test/**/*.test.js\""}}