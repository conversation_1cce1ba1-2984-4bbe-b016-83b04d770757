---
const { lang = 'en' } = Astro.props;

const footerContent = {
  en: {
    company: 'Company',
    quickLinks: 'Quick Links',
    followUs: 'Follow Us',
    contactInfo: 'Contact Info',
    address: '2nd Floor, Omanye Aba building, opposite Ohene Djan Sports Stadium',
    phone: '+233 24-388-9991',
    email: '<EMAIL>',
    copyright: '© 2024 Power Telco Business Limited. All rights reserved.',
    links: [
      { name: 'About Us', url: '/about' },
      { name: 'Services', url: '/services' },
      { name: 'Team', url: '/team' },
      { name: 'Contact', url: '/contact' },
      { name: 'Privacy Policy', url: '/privacy' },
      { name: 'Terms of Service', url: '/terms' }
    ]
  },
  fr: {
    company: 'Entreprise',
    quickLinks: 'Liens Rapides',
    followUs: 'Suivez-nous',
    contactInfo: 'Informations de Contact',
    address: '2ème étage, bâtiment Omanye Aba, en face du stade Ohene Djan Sports',
    phone: '+233 24-388-9991',
    email: '<EMAIL>',
    copyright: '© 2024 Power Telco Business Limited. Tous droits réservés.',
    links: [
      { name: 'À propos', url: '/fr/about' },
      { name: 'Services', url: '/fr/services' },
      { name: 'Équipe', url: '/fr/team' },
      { name: 'Contact', url: '/fr/contact' },
      { name: 'Politique de confidentialité', url: '/fr/privacy' },
      { name: 'Conditions de service', url: '/fr/terms' }
    ]
  }
};

const content = footerContent[lang as keyof typeof footerContent] || footerContent.en;
---

<footer class="bg-gray-900 text-white">
  <div class="container">
    <div class="py-16">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- Company Info -->
        <div class="space-y-4">
          <img src="/images/ptbl-logo.png" alt="PTBL" class="h-12 w-auto mb-4">
          <p class="text-gray-300 text-sm leading-relaxed">
            We aim to leverage ECG’s goodwill, fiber optic assets and technical capacities to become the leading provider of innovative and sustainable solutions to ECG and other entities in the subregion.
          </p>
          <div class="flex space-x-4">
            <a href="https://www.linkedin.com/company/power-telco-business-limted/" target="_blank" rel="noopener noreferrer" 
               class="text-gray-400 hover:text-primary transition-colors">
              <i class="fab fa-linkedin-in text-xl"></i>
            </a>
            <!-- <a href="#" class="text-gray-400 hover:text-primary transition-colors">
              <i class="fab fa-facebook-f text-xl"></i>
            </a>
            <a href="#" class="text-gray-400 hover:text-primary transition-colors">
              <i class="fab fa-twitter text-xl"></i>
            </a> -->
          </div>
        </div>

        <!-- Quick Links -->
        <div class="space-y-4">
          <h4 class="text-lg font-semibold mb-4">{content.quickLinks}</h4>
          <ul class="space-y-2">
            {content.links.slice(0, 4).map((link) => (
              <li>
                <a href={link.url} class="text-gray-300 hover:text-primary transition-colors text-sm">
                  {link.name}
                </a>
              </li>
            ))}
          </ul>
        </div>

        <!-- Legal Links -->
        <div class="space-y-4">
          <h4 class="text-lg font-semibold mb-4">{content.company}</h4>
          <ul class="space-y-2">
            {content.links.slice(4).map((link) => (
              <li>
                <a href={link.url} class="text-gray-300 hover:text-primary transition-colors text-sm">
                  {link.name}
                </a>
              </li>
            ))}
          </ul>
        </div>

        <!-- Contact Info -->
        <div class="space-y-4">
          <h4 class="text-lg font-semibold mb-4">{content.contactInfo}</h4>
          <div class="space-y-3">
            <div class="flex items-start space-x-3">
              <i class="fas fa-map-marker-alt text-primary mt-1"></i>
              <p class="text-gray-300 text-sm">{content.address}</p>
            </div>
            <div class="flex items-center space-x-3">
              <i class="fas fa-phone text-primary"></i>
              <a href={`tel:${content.phone}`} class="text-gray-300 hover:text-primary transition-colors text-sm">
                {content.phone}
              </a>
            </div>
            <div class="flex items-center space-x-3">
              <i class="fas fa-envelope text-primary"></i>
              <a href={`mailto:${content.email}`} class="text-gray-300 hover:text-primary transition-colors text-sm">
                {content.email}
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Copyright -->
    <div class="border-t border-gray-800 py-6">
      <div class="flex flex-col md:flex-row justify-between items-center">
        <p class="text-gray-400 text-sm">{content.copyright}</p>
        <div class="flex items-center space-x-4 mt-4 md:mt-0">
          <span class="text-gray-400 text-sm">Powered by</span>
          <a href="https://termitetechlab.com" target="_blank" rel="noopener noreferrer" 
             class="text-primary hover:text-blue-400 transition-colors text-sm font-medium">
            Termite Tech Lab
          </a>
        </div>
      </div>
    </div>
  </div>
</footer>
