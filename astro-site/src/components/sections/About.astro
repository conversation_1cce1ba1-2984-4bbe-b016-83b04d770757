---
export interface Props {
  enable: boolean;
  about_item: Array<{
    image: string;
    subtitle?: string;
    title: string;
    content: string;
    button: {
      enable: boolean;
      label: string;
      link: string;
    };
  }>;
}

const { enable, about_item } = Astro.props;
---

{enable && (
  <section class="section">
    <div class="container">
      {about_item.map((item, index) => (
        <div class={`flex flex-col lg:flex-row items-center gap-16 ${index > 0 ? 'mt-32' : ''}`}>
          <!-- Image Section with Enhanced Styling -->
          <div class={`lg:w-1/2 ${index % 2 === 1 ? 'lg:order-2' : ''}`}>
            <div class="relative group">
              <img
                src={item.image}
                alt={item.title}
                class="w-full h-auto rounded-2xl shadow-2xl transition-transform duration-500 group-hover:scale-105"
                data-aos={index % 2 === 0 ? "fade-right" : "fade-left"}
              >
              <!-- Decorative elements -->
              <div class="absolute -top-4 -left-4 w-24 h-24 bg-primary/10 rounded-full blur-xl"></div>
              <div class="absolute -bottom-4 -right-4 w-32 h-32 bg-blue-500/10 rounded-full blur-xl"></div>
            </div>
          </div>

          <!-- Content Section with Enhanced Typography -->
          <div class={`lg:w-1/2 ${index % 2 === 1 ? 'lg:order-1' : ''}`}>
            {item.subtitle && (
              <p class="subtitle" data-aos="fade-up">{item.subtitle}</p>
            )}
            <h2 class="section-title mb-8 text-4xl lg:text-5xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent" data-aos="fade-up" data-aos-delay="200">
              {item.title}
            </h2>

            <!-- Enhanced content rendering with proper HTML support -->
            <div
              class="prose prose-lg max-w-none text-gray-600 leading-relaxed mb-10 space-y-4"
              data-aos="fade-up"
              data-aos-delay="400"
              set:html={item.content}
            />

            {item.button.enable && (
              <div data-aos="fade-up" data-aos-delay="600">
                <a
                  href={item.button.link}
                  class="inline-flex items-center px-8 py-4 bg-primary text-white font-semibold rounded-full hover:bg-blue-700 transition-all duration-300 transform hover:scale-105 hover:shadow-lg group"
                >
                  <span>{item.button.label}</span>
                  <svg class="ml-2 w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                  </svg>
                </a>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  </section>
)}
