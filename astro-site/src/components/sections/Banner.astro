---
export interface Props {
  enable: boolean;
  bg_image: string;
  title: string;
  subtitle?: string;
  watermark: string;
  content: string;
  features?: string[];
  image?: string;
  button: {
    enable: boolean;
    label: string;
    link: string;
  };
  secondary_button?: {
    enable: boolean;
    label: string;
    link: string;
  };
}

const { enable, bg_image, title, subtitle, watermark, content, features, image, button, secondary_button } = Astro.props;
---

{enable && (
  <section class="hero-area bg-cover relative min-h-screen flex items-center overflow-hidden" style={`background-image: url('${bg_image}')`}>
    <!-- Enhanced background overlay with gradient -->
    <div class="absolute inset-0 bg-gradient-to-br from-black/70 via-black/50 to-black/60"></div>

    <!-- Animated background elements -->
    <div class="absolute inset-0">
      <div class="absolute top-1/4 left-1/4 w-64 h-64 bg-primary/10 rounded-full blur-3xl animate-pulse"></div>
      <div class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-secondary/10 rounded-full blur-3xl animate-pulse" style="animation-delay: 2s;"></div>
    </div>

    <div class="container relative z-10">
      <div class="flex items-center justify-between min-h-[80vh]">
        <div class={`${image ? 'lg:w-1/2' : 'w-full'} text-center lg:text-left`}>
          <!-- Subtitle -->
          {subtitle && (
            <div class="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full text-white text-sm font-medium mb-6" data-aos="fade-up" data-aos-delay="800">
              <span class="w-2 h-2 bg-primary rounded-full mr-2 animate-pulse"></span>
              {subtitle}
            </div>
          )}

          <!-- Main title with enhanced mobile typography -->
          <h1 class="text-white text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold leading-tight mb-6 relative" data-aos="fade-up" data-aos-delay="1000">
            {title}
            <span class="watermark absolute -top-4 sm:-top-8 -right-4 sm:-right-8 text-6xl sm:text-8xl lg:text-9xl font-black opacity-10 pointer-events-none">
              {watermark}
            </span>
          </h1>

          <!-- Content with better mobile spacing -->
          <p class="text-white/90 text-base sm:text-lg lg:text-xl leading-relaxed mb-8 max-w-2xl mx-auto lg:mx-0" data-aos="fade-up" data-aos-delay="1200" set:html={content}></p>

          <!-- Features list -->
          {features && features.length > 0 && (
            <div class="flex flex-wrap justify-center lg:justify-start gap-4 mb-8" data-aos="fade-up" data-aos-delay="1300">
              {features.map((feature) => (
                <div class="flex items-center text-white/80 text-sm">
                  <svg class="w-4 h-4 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  {feature}
                </div>
              ))}
            </div>
          )}

          <!-- Enhanced button group with mobile-first design -->
          <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start" data-aos="fade-up" data-aos-delay="1400">
            {button.enable && (
              <a href={button.link} class="btn btn-primary text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 font-semibold shadow-xl hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-300">
                {button.label}
                <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                </svg>
              </a>
            )}
            {secondary_button?.enable && (
              <a href={secondary_button.link} class="btn btn-outline-white text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 font-semibold backdrop-blur-sm hover:bg-white/10 transition-all duration-300">
                {secondary_button.label}
              </a>
            )}
          </div>
        </div>

        {image && (
          <div class="hidden lg:block lg:w-1/2 text-center">
            <img src={image} class="max-w-full h-auto drop-shadow-2xl" alt="Hero illustration" data-aos="zoom-in" data-aos-delay="1500">
          </div>
        )}
      </div>
    </div>

    <!-- Enhanced scroll indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
      <div class="flex flex-col items-center">
        <span class="text-xs mb-2 opacity-75">Scroll Down</span>
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
        </svg>
      </div>
    </div>
  </section>
)}

<style>
  .hero-area {
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    min-height: 100vh;
    min-height: 100dvh; /* Dynamic viewport height for mobile */
  }

  /* Mobile-first responsive adjustments */
  @media (max-width: 768px) {
    .hero-area {
      background-attachment: scroll;
      min-height: 100vh;
      padding: 2rem 0;
    }

    .watermark {
      display: none !important; /* Hide watermark on very small screens */
    }
  }

  @media (max-width: 480px) {
    .hero-area {
      min-height: 90vh;
    }
  }

  /* Enhanced animations */
  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-20px);
    }
  }

  .hero-area .animate-pulse {
    animation: float 6s ease-in-out infinite;
  }

  /* Button hover enhancements */
  .btn {
    position: relative;
    overflow: hidden;
  }

  .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  .btn:hover::before {
    left: 100%;
  }

  /* Improved text shadows for better readability */
  .hero-area h1 {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  }

  .hero-area p {
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  }

  /* Responsive watermark */
  .watermark {
    user-select: none;
    pointer-events: none;
  }

  @media (max-width: 1024px) {
    .watermark {
      font-size: 6rem !important;
      top: -2rem !important;
      right: -2rem !important;
    }
  }

  @media (max-width: 640px) {
    .watermark {
      font-size: 4rem !important;
      top: -1rem !important;
      right: -1rem !important;
      opacity: 0.05 !important;
    }
  }
</style>
