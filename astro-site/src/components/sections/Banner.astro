---
export interface Props {
  enable: boolean;
  bg_image: string;
  title: string;
  watermark: string;
  content: string;
  image?: string;
  button: {
    enable: boolean;
    label: string;
    link: string;
  };
}

const { enable, bg_image, title, watermark, content, image, button } = Astro.props;
---

{enable && (
  <section class="hero-area bg-cover relative min-h-screen flex items-center" style={`background-image: url('${bg_image}')`}>
    <div class="absolute inset-0 bg-black bg-opacity-50"></div>
    <div class="container relative z-10">
      <div class="flex items-center justify-between min-h-[80vh]">
        <div class={`${image ? 'lg:w-1/2' : 'w-full'} text-center lg:text-left`}>
          <h1 class="text-white text-4xl lg:text-6xl xl:text-7xl font-bold leading-tight mb-6 relative" data-aos="fade-up" data-aos-delay="1000">
            {title}
            <span class="watermark absolute -top-8 -right-8 text-8xl lg:text-9xl font-black opacity-10 pointer-events-none">
              {watermark}
            </span>
          </h1>
          <p class="text-white text-lg lg:text-xl leading-relaxed mb-8 max-w-2xl" data-aos="fade-up" data-aos-delay="1200" set:html={content}></p>
          {button.enable && (
            <a href={button.link} class="btn btn-primary text-lg px-8 py-4" data-aos="fade-up" data-aos-delay="1400">
              {button.label}
            </a>
          )}
        </div>
        {image && (
          <div class="hidden lg:block lg:w-1/2 text-center">
            <img src={image} class="max-w-full h-auto" alt="Hero illustration" data-aos="zoom-in" data-aos-delay="1500">
          </div>
        )}
      </div>
    </div>
    
    <!-- Scroll indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
      <i class="fas fa-chevron-down text-2xl"></i>
    </div>
  </section>
)}

<style>
  .hero-area {
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
  }
  
  @media (max-width: 768px) {
    .hero-area {
      background-attachment: scroll;
    }
  }
</style>
