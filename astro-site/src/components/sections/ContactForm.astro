---
export interface Props {
  lang?: string;
}

const { lang = 'en' } = Astro.props;

const content = {
  en: {
    // title: "Get In Touch",
    // subtitle: "Contact Us",
    // description: "Ready to enhance your connectivity? Contact us today to discuss your fiber network requirements.",
    form: {
      name: "Full Name",
      email: "Email Address",
      phone: "Phone Number",
      subject: "Subject",
      message: "Message",
      submit: "Send Message"
    },
    info: {
      address: "2nd Floor, Omanye Aba building, opposite Ohene Djan Sports Stadium",
      phone: "+233 24-388-9991",
      email: "<EMAIL>",
      hours: "Monday - Friday: 8:00 AM - 5:00 PM"
    }
  },
  fr: {
    title: "Contactez-nous",
    subtitle: "Contact",
    description: "Prêt à améliorer votre connectivité? Contactez-nous aujourd'hui pour discuter de vos besoins en réseau de fibres.",
    form: {
      name: "Nom Complet",
      email: "Adresse Email",
      phone: "Numéro de Téléphone",
      subject: "Sujet",
      message: "Message",
      submit: "<PERSON>voy<PERSON> le Message"
    },
    info: {
      address: "2ème étage, bâtiment Omanye Aba, en face du stade Ohene Djan Sports",
      phone: "+233 24-388-9991",
      email: "<EMAIL>",
      hours: "Lundi - Vendredi: 8h00 - 17h00"
    }
  }
};

const text = content[lang as keyof typeof content] || content.en;
---

<section class="section">
  <div class="container">
    <div class="text-center mb-16">
      <p class="subtitle" data-aos="fade-up">{text.subtitle}</p>
      <h2 class="section-title mb-6" data-aos="fade-up" data-aos-delay="200">{text.title}</h2>
      <p class="text-color text-lg max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="400">
        {text.description}
      </p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
      <!-- Contact Form -->
      <div class="card p-8" data-aos="fade-right">
        <form id="contact-form" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                {text.form.name} *
              </label>
              <input 
                type="text" 
                id="name" 
                name="name" 
                required
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300"
              >
            </div>
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                {text.form.email} *
              </label>
              <input 
                type="email" 
                id="email" 
                name="email" 
                required
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300"
              >
            </div>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                {text.form.phone}
              </label>
              <input 
                type="tel" 
                id="phone" 
                name="phone"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300"
              >
            </div>
            <div>
              <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">
                {text.form.subject} *
              </label>
              <input 
                type="text" 
                id="subject" 
                name="subject" 
                required
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300"
              >
            </div>
          </div>
          
          <div>
            <label for="message" class="block text-sm font-medium text-gray-700 mb-2">
              {text.form.message} *
            </label>
            <textarea 
              id="message" 
              name="message" 
              rows="6" 
              required
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300 resize-vertical"
            ></textarea>
          </div>
          
          <button 
            type="submit" 
            class="btn btn-primary w-full"
            id="submit-btn"
          >
            <span id="submit-text">{text.form.submit}</span>
            <span id="submit-loading" class="hidden">
              <i class="fas fa-spinner fa-spin mr-2"></i>
              Sending...
            </span>
          </button>
        </form>
      </div>

      <!-- Contact Information -->
      <div class="space-y-8" data-aos="fade-left">
        <div class="card p-8">
          <h3 class="text-xl font-semibold mb-6">Contact Information</h3>
          <div class="space-y-6">
            <div class="flex items-start space-x-4">
              <div class="w-12 h-12 bg-primary bg-opacity-10 rounded-lg flex items-center justify-center flex-shrink-0">
                <i class="fas fa-map-marker-alt text-primary text-lg"></i>
              </div>
              <div>
                <h4 class="font-medium mb-1">Address</h4>
                <p class="text-color">{text.info.address}</p>
              </div>
            </div>
            
            <div class="flex items-start space-x-4">
              <div class="w-12 h-12 bg-primary bg-opacity-10 rounded-lg flex items-center justify-center flex-shrink-0">
                <i class="fas fa-phone text-primary text-lg"></i>
              </div>
              <div>
                <h4 class="font-medium mb-1">Phone</h4>
                <a href={`tel:${text.info.phone}`} class="text-color hover:text-primary transition-colors">
                  {text.info.phone}
                </a>
              </div>
            </div>
            
            <div class="flex items-start space-x-4">
              <div class="w-12 h-12 bg-primary bg-opacity-10 rounded-lg flex items-center justify-center flex-shrink-0">
                <i class="fas fa-envelope text-primary text-lg"></i>
              </div>
              <div>
                <h4 class="font-medium mb-1">Email</h4>
                <a href={`mailto:${text.info.email}`} class="text-color hover:text-primary transition-colors">
                  {text.info.email}
                </a>
              </div>
            </div>
            
            <div class="flex items-start space-x-4">
              <div class="w-12 h-12 bg-primary bg-opacity-10 rounded-lg flex items-center justify-center flex-shrink-0">
                <i class="fas fa-clock text-primary text-lg"></i>
              </div>
              <div>
                <h4 class="font-medium mb-1">Business Hours</h4>
                <p class="text-color">{text.info.hours}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('contact-form');
    const submitBtn = document.getElementById('submit-btn');
    const submitText = document.getElementById('submit-text');
    const submitLoading = document.getElementById('submit-loading');
    
    if (form && submitBtn && submitText && submitLoading) {
      form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        // Show loading state
        submitBtn.disabled = true;
        submitText.classList.add('hidden');
        submitLoading.classList.remove('hidden');
        
        // Simulate form submission (replace with actual form handling)
        try {
          await new Promise(resolve => setTimeout(resolve, 2000));
          
          // Show success message
          alert('Thank you for your message! We will get back to you soon.');
          form.reset();
        } catch (error) {
          alert('Sorry, there was an error sending your message. Please try again.');
        } finally {
          // Reset button state
          submitBtn.disabled = false;
          submitText.classList.remove('hidden');
          submitLoading.classList.add('hidden');
        }
      });
    }
  });
</script>
