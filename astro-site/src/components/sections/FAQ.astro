---
export interface Props {
  enable: boolean;
  subtitle?: string;
  title?: string;
  faq_item: Array<{
    question: string;
    answer: string;
  }>;
}

const { enable, subtitle, title, faq_item } = Astro.props;
---

{enable && (
  <section class="section">
    <div class="container">
      {(subtitle || title) && (
        <div class="text-center mb-16">
          {subtitle && <p class="subtitle" data-aos="fade-up">{subtitle}</p>}
          {title && <h2 class="section-title" data-aos="fade-up" data-aos-delay="200">{title}</h2>}
        </div>
      )}
      
      <div class="max-w-4xl mx-auto">
        <div class="space-y-4">
          {faq_item.map((item, index) => (
            <div class="card overflow-hidden" data-aos="fade-up" data-aos-delay={100 * (index + 1)}>
              <button 
                class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-300 faq-toggle"
                data-target={`faq-${index}`}
              >
                <h4 class="text-lg font-semibold text-gray-900 pr-4">{item.question}</h4>
                <i class="fas fa-chevron-down text-primary transition-transform duration-300 faq-icon"></i>
              </button>
              <div 
                id={`faq-${index}`} 
                class="faq-content max-h-0 overflow-hidden transition-all duration-300 ease-in-out"
              >
                <div class="px-6 pb-4">
                  <div class="border-t pt-4">
                    <p class="text-color leading-relaxed" set:html={item.answer}></p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div class="text-center mt-12">
          <p class="text-color text-lg mb-6">
            Still have questions? We're here to help.
          </p>
          <a href="/contact" class="btn btn-primary">
            Contact Our Support Team
          </a>
        </div>
      </div>
    </div>
  </section>
)}

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const faqToggles = document.querySelectorAll('.faq-toggle');
    
    faqToggles.forEach(toggle => {
      toggle.addEventListener('click', function() {
        const target = this.getAttribute('data-target');
        const content = document.getElementById(target);
        const icon = this.querySelector('.faq-icon');
        
        if (content && icon) {
          const isOpen = content.style.maxHeight && content.style.maxHeight !== '0px';
          
          // Close all other FAQ items
          faqToggles.forEach(otherToggle => {
            if (otherToggle !== this) {
              const otherTarget = otherToggle.getAttribute('data-target');
              const otherContent = document.getElementById(otherTarget);
              const otherIcon = otherToggle.querySelector('.faq-icon');
              
              if (otherContent && otherIcon) {
                otherContent.style.maxHeight = '0px';
                otherIcon.style.transform = 'rotate(0deg)';
              }
            }
          });
          
          // Toggle current FAQ item
          if (isOpen) {
            content.style.maxHeight = '0px';
            icon.style.transform = 'rotate(0deg)';
          } else {
            content.style.maxHeight = content.scrollHeight + 'px';
            icon.style.transform = 'rotate(180deg)';
          }
        }
      });
    });
  });
</script>
