---
export interface Props {
  enable: boolean;
  subtitle: string;
  title: string;
  feature_item: Array<{
    name: string;
    image: string;
    content: string;
  }>;
}

const { enable, subtitle, title, feature_item } = Astro.props;
---

{enable && (
  <section class="section bg-gray-50">
    <div class="container">
      <div class="text-center mb-16">
        <p class="subtitle" data-aos="fade-up">{subtitle}</p>
        <h2 class="section-title" data-aos="fade-up" data-aos-delay="200">{title}</h2>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {feature_item.map((item, index) => (
          <div class="group card overflow-hidden hover:shadow-xl hover:-translate-y-2 transition-all duration-300" data-aos="fade-up" data-aos-delay={200 * (index + 1)}>
            <!-- Full-width image section -->
            <div class="relative overflow-hidden">
              <img
                src={item.image}
                alt={item.name}
                class="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
              >
              <!-- Gradient overlay for better text readability -->
              <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>

            <!-- Content section -->
            <div class="p-6">
              <h4 class="text-xl font-semibold mb-3 text-gray-900 group-hover:text-primary transition-colors duration-300">
                {item.name}
              </h4>
              <p class="text-gray-600 leading-relaxed text-sm">
                {item.content}
              </p>
            </div>

            <!-- Hover indicator -->
            <div class="absolute top-4 right-4 w-8 h-8 bg-white/90 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-2 group-hover:translate-x-0">
              <i class="fas fa-arrow-right text-primary text-sm"></i>
            </div>
          </div>
        ))}
      </div>
    </div>
  </section>
)}
