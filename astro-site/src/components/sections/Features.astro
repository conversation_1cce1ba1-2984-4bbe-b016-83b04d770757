---
export interface Props {
  enable: boolean;
  subtitle: string;
  title: string;
  description?: string;
  feature_item: Array<{
    name: string;
    image: string;
    content: string;
    icon?: string;
    color?: string;
  }>;
}

const { enable, subtitle, title, description, feature_item } = Astro.props;

// Get current language from URL
const currentPath = Astro.url.pathname;
const isFrenchlang = currentPath.startsWith('/fr');

// Localized text
const localizedText = {
  allServicesAvailable: isFrenchlang ? "Tous Services Disponibles" : "All Services Available",
  quickSetup: isFrenchlang ? "Configuration Rapide" : "Quick Setup"
};
---

{enable && (
  <section class="section bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
    <!-- Background decorative elements -->
    <div class="absolute top-0 left-0 w-72 h-72 bg-primary/5 rounded-full blur-3xl -translate-x-1/2 -translate-y-1/2"></div>
    <div class="absolute bottom-0 right-0 w-96 h-96 bg-secondary/5 rounded-full blur-3xl translate-x-1/2 translate-y-1/2"></div>

    <div class="container relative z-10">
      <!-- Enhanced header section -->
      <div class="text-center mb-16 lg:mb-20">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-primary to-secondary rounded-full mb-6" data-aos="zoom-in">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
          </svg>
        </div>
        <p class="subtitle" data-aos="fade-up">{subtitle}</p>
        <h2 class="section-title max-w-4xl mx-auto" data-aos="fade-up" data-aos-delay="200">{title}</h2>
        {description && (
          <p class="text-lg text-gray-600 max-w-3xl mx-auto mt-6" data-aos="fade-up" data-aos-delay="400">
            {description}
          </p>
        )}
        <div class="w-24 h-1 bg-gradient-to-r from-primary to-secondary mx-auto mt-8" data-aos="fade-up" data-aos-delay="600"></div>
      </div>

      <!-- Enhanced features grid with mobile-first design -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
        {feature_item.map((item, index) => (
          <div class="group feature-card bg-white rounded-2xl overflow-hidden hover:shadow-2xl hover:-translate-y-3 transition-all duration-500 border border-gray-100 hover:border-transparent" data-aos="fade-up" data-aos-delay={150 * (index + 1)}>
            <!-- Enhanced image section with overlay -->
            <div class="relative overflow-hidden h-48 sm:h-56">
              <img
                src={item.image}
                alt={item.name}
                class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
              >
              <!-- Gradient overlay -->
              <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>

              <!-- Icon overlay -->
              {item.icon && (
                <div class="absolute top-4 left-4 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center">
                  <i class={`${item.icon} text-white text-xl`}></i>
                </div>
              )}

              <!-- Enhanced hover overlay for visual appeal -->
              <div class="absolute inset-0 bg-gradient-to-t from-black/40 via-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>

            <!-- Enhanced content section -->
            <div class="p-6 lg:p-8">
              <h4 class="text-xl lg:text-2xl font-bold mb-4 text-gray-900 group-hover:text-primary transition-colors duration-300 leading-tight">
                {item.name}
              </h4>
              <p class="text-gray-600 leading-relaxed text-sm lg:text-base group-hover:text-gray-700 transition-colors duration-300">
                {item.content}
              </p>
            </div>

            <!-- Service number badge -->
            <div class="absolute top-4 right-4 w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center text-sm font-bold text-gray-600 group-hover:bg-primary group-hover:text-white transition-all duration-300">
              {String(index + 1).padStart(2, '0')}
            </div>
          </div>
        ))}
      </div>

      <!-- Call to action -->
      <div class="text-center mt-16" data-aos="fade-up" data-aos-delay="800">
        <div class="inline-flex items-center justify-center space-x-4 bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
            <span class="text-sm font-medium text-gray-600">{localizedText.allServicesAvailable}</span>
          </div>
          <div class="w-px h-6 bg-gray-200"></div>
          <div class="flex items-center space-x-2">
            <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="text-sm font-medium text-gray-600">{localizedText.quickSetup}</span>
          </div>
        </div>
      </div>
    </div>
  </section>
)}

<style>
  /* Enhanced Feature Card Styles */
  .feature-card {
    position: relative;
    backdrop-filter: blur(10px);
  }

  .feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
    border-radius: 1rem;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .feature-card:hover::before {
    opacity: 1;
  }

  /* Mobile-first responsive adjustments */
  @media (max-width: 768px) {
    .feature-card {
      margin-bottom: 1rem;
    }

    .feature-card .p-6 {
      padding: 1.25rem;
    }
  }

  /* Enhanced hover effects */
  .feature-card:hover {
    transform: translateY(-12px) scale(1.02);
  }

  /* Image overlay animations */
  .feature-card .absolute.inset-0 {
    transition: all 0.3s ease;
  }

  /* Responsive image heights */
  @media (max-width: 640px) {
    .feature-card .h-48 {
      height: 12rem;
    }
  }

  @media (min-width: 641px) and (max-width: 1024px) {
    .feature-card .sm\\:h-56 {
      height: 14rem;
    }
  }

  /* Loading animation for images */
  .feature-card img {
    transition: opacity 0.3s ease;
  }

  .feature-card img[loading] {
    opacity: 0;
  }

  .feature-card img[loading="lazy"] {
    opacity: 1;
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Add intersection observer for enhanced animations
    const featureCards = document.querySelectorAll('.feature-card');

    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');

          // Add staggered animation to child elements
          const image = entry.target.querySelector('img');
          const title = entry.target.querySelector('h4');
          const content = entry.target.querySelector('p');

          if (image) {
            setTimeout(() => image.classList.add('animate-fade-in'), 200);
          }
          if (title) {
            setTimeout(() => title.classList.add('animate-fade-in'), 400);
          }
          if (content) {
            setTimeout(() => content.classList.add('animate-fade-in'), 600);
          }
        }
      });
    }, observerOptions);

    featureCards.forEach(card => {
      observer.observe(card);
    });

    // Lazy loading for images
    const images = document.querySelectorAll('.feature-card img');
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          img.src = img.src;
          img.classList.add('loaded');
          imageObserver.unobserve(img);
        }
      });
    });

    images.forEach(img => imageObserver.observe(img));
  });
</script>
