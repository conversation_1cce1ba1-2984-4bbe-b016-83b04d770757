---
export interface Props {
  enable: boolean;
  video_thumbnail: string;
  video_link?: string;
  subtitle: string;
  title: string;
  content: string;
}

const { enable, video_thumbnail, video_link, subtitle, title, content } = Astro.props;
---

{enable && (
  <section class="section bg-gray-50">
    <div class="container">
      <div class="flex flex-col lg:flex-row items-center gap-12">
        <div class="lg:w-1/2">
          <div class="relative">
            <img 
              src={video_thumbnail} 
              alt="Video thumbnail" 
              class="w-full h-auto rounded-lg shadow-lg"
              data-aos="fade-right"
            >
            {video_link && (
              <button 
                class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 rounded-lg hover:bg-opacity-50 transition-all duration-300"
                onclick={`openVideoModal('${video_link}')`}
                data-aos="zoom-in"
                data-aos-delay="400"
              >
                <div class="w-20 h-20 bg-primary rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-300">
                  <i class="fas fa-play text-white text-2xl ml-1"></i>
                </div>
              </button>
            )}
          </div>
        </div>
        
        <div class="lg:w-1/2">
          <p class="subtitle" data-aos="fade-up">{subtitle}</p>
          <h2 class="section-title mb-6" data-aos="fade-up" data-aos-delay="200">{title}</h2>
          <div class="text-color text-lg leading-relaxed" data-aos="fade-up" data-aos-delay="400" set:html={content}></div>
        </div>
      </div>
    </div>
    
    <!-- Video Modal -->
    {video_link && (
      <div id="videoModal" class="fixed inset-0 bg-black bg-opacity-75 z-50 hidden flex items-center justify-center p-4">
        <div class="relative w-full max-w-4xl">
          <button 
            onclick="closeVideoModal()" 
            class="absolute -top-12 right-0 text-white text-2xl hover:text-gray-300 transition-colors"
          >
            <i class="fas fa-times"></i>
          </button>
          <div class="relative pb-[56.25%] h-0 overflow-hidden rounded-lg">
            <iframe 
              id="videoFrame"
              class="absolute top-0 left-0 w-full h-full"
              frameborder="0"
              allowfullscreen
            ></iframe>
          </div>
        </div>
      </div>
    )}
  </section>
)}

<script>
  function openVideoModal(videoUrl) {
    const modal = document.getElementById('videoModal');
    const iframe = document.getElementById('videoFrame');
    
    if (modal && iframe) {
      // Convert YouTube URL to embed URL
      const embedUrl = videoUrl.replace('watch?v=', 'embed/') + '?autoplay=1';
      iframe.src = embedUrl;
      modal.classList.remove('hidden');
      document.body.style.overflow = 'hidden';
    }
  }
  
  function closeVideoModal() {
    const modal = document.getElementById('videoModal');
    const iframe = document.getElementById('videoFrame');
    
    if (modal && iframe) {
      iframe.src = '';
      modal.classList.add('hidden');
      document.body.style.overflow = 'auto';
    }
  }
  
  // Close modal on escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      closeVideoModal();
    }
  });
</script>
