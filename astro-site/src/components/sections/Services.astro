---
export interface Props {
  enable: boolean;
  subtitle: string;
  title: string;
  service_item: Array<{
    title: string;
    icon: string;
    icon_color: string;
    content: string;
  }>;
}

const { enable, subtitle, title, service_item } = Astro.props;
---

{enable && (
  <section class="section">
    <div class="container">
      <div class="text-center mb-16">
        <p class="subtitle" data-aos="fade-up">{subtitle}</p>
        <h2 class="section-title" data-aos="fade-up" data-aos-delay="200">{title}</h2>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {service_item.map((item, index) => (
          <div class="card p-8 text-center hover:shadow-xl transition-all duration-300 group" data-aos="fade-up" data-aos-delay={200 * (index + 1)}>
            <div class={`w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center bg-${item.icon_color}-100 group-hover:bg-${item.icon_color}-500 transition-all duration-300`}>
              <i class={`${item.icon} text-3xl text-${item.icon_color}-500 group-hover:text-white transition-all duration-300`}></i>
            </div>
            <h4 class="text-xl font-semibold mb-4 text-gray-900">{item.title}</h4>
            <p class="text-color leading-relaxed" set:html={item.content}></p>
          </div>
        ))}
      </div>
    </div>
  </section>
)}
