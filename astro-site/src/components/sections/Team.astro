---
export interface Props {
  enable: boolean;
  subtitle?: string;
  title?: string;
  team_member: Array<{
    name: string;
    image: string;
    designation: string;
    bio?: string;
    social?: Array<{
      icon: string;
      link: string;
    }>;
  }>;
}

const { enable, subtitle, title, team_member } = Astro.props;
---

{enable && (
  <section class="section">
    <div class="container">
      {(subtitle || title) && (
        <div class="text-center mb-16">
          {subtitle && <p class="subtitle" data-aos="fade-up">{subtitle}</p>}
          {title && <h2 class="section-title" data-aos="fade-up" data-aos-delay="200">{title}</h2>}
        </div>
      )}

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {team_member.map((member, index) => (
          <div class="card overflow-hidden hover:shadow-xl transition-all duration-300" data-aos="fade-up" data-aos-delay={200 * (index + 1)}>
            <div class="flex flex-col md:flex-row">
              <!-- Image Section -->
              <div class="md:w-1/3 flex-shrink-0">
                <img
                  src={member.image}
                  alt={member.name}
                  class="w-full h-64 md:h-full object-cover"
                >
              </div>

              <!-- Content Section -->
              <div class="md:w-2/3 p-6 flex flex-col">
                <div class="mb-4">
                  <h4 class="text-xl font-bold text-gray-900 mb-2">{member.name}</h4>
                  <p class="text-primary font-semibold mb-3">{member.designation}</p>
                </div>

                {member.bio && (
                  <div class="flex-1">
                    <p class="text-gray-600 text-sm leading-relaxed line-clamp-6 mb-4">
                      {member.bio}
                    </p>

                    <button
                      class="text-primary hover:text-blue-700 font-medium text-sm transition-colors duration-300 mb-4"
                      onclick={`toggleBio('bio-${index}')`}
                    >
                      Read More
                    </button>
                  </div>
                )}

                {member.social && member.social.length > 0 && (
                  <div class="flex space-x-3 mt-auto">
                    {member.social.map((social) => (
                      <a
                        href={social.link}
                        target="_blank"
                        rel="noopener noreferrer"
                        class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center hover:bg-primary hover:text-white transition-all duration-300"
                      >
                        <i class={`${social.icon}`}></i>
                      </a>
                    ))}
                  </div>
                )}
              </div>
            </div>

            <!-- Expandable Bio Section -->
            {member.bio && (
              <div id={`bio-${index}`} class="hidden border-t bg-gray-50 p-6">
                <h5 class="font-semibold text-gray-900 mb-3">About {member.name}</h5>
                <div class="text-gray-600 text-sm leading-relaxed space-y-3">
                  {member.bio.split('\n\n').map((paragraph) => (
                    <p>{paragraph.trim()}</p>
                  ))}
                </div>
                <button
                  class="text-primary hover:text-blue-700 font-medium text-sm transition-colors duration-300 mt-4"
                  onclick={`toggleBio('bio-${index}')`}
                >
                  Show Less
                </button>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>

    <!-- JavaScript for bio toggle -->
    <script>
      function toggleBio(bioId) {
        const bioElement = document.getElementById(bioId);
        const isHidden = bioElement.classList.contains('hidden');

        if (isHidden) {
          bioElement.classList.remove('hidden');
          bioElement.style.maxHeight = bioElement.scrollHeight + 'px';
        } else {
          bioElement.style.maxHeight = '0px';
          setTimeout(() => {
            bioElement.classList.add('hidden');
          }, 300);
        }
      }

      // Make function globally available
      window.toggleBio = toggleBio;
    </script>

    <style>
      .line-clamp-6 {
        display: -webkit-box;
        -webkit-line-clamp: 6;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      #bio-0, #bio-1, #bio-2, #bio-3, #bio-4 {
        transition: max-height 0.3s ease-in-out;
        overflow: hidden;
      }
    </style>
  </section>
)}
