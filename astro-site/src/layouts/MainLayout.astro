---
import BaseLayout from './BaseLayout.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';

export interface Props {
  title: string;
  description?: string;
  image?: string;
  lang?: string;
  showPageHeader?: boolean;
  pageTitle?: string;
  pageSubtitle?: string;
}

const { 
  title, 
  description, 
  image, 
  lang = 'en',
  showPageHeader = false,
  pageTitle,
  pageSubtitle
} = Astro.props;
---

<BaseLayout title={title} description={description} image={image} lang={lang}>
  <Header lang={lang} />
  
  {showPageHeader && (
    <section class="bg-gradient-to-r from-primary to-secondary py-20 mt-20">
      <div class="container">
        <div class="text-center text-white">
          <h1 class="text-4xl lg:text-5xl font-bold mb-4" data-aos="fade-up">
            {pageTitle || title}
          </h1>
          {pageSubtitle && (
            <p class="text-xl opacity-90" data-aos="fade-up" data-aos-delay="200">
              {pageSubtitle}
            </p>
          )}
        </div>
      </div>
    </section>
  )}
  
  <main class={showPageHeader ? '' : 'pt-20'}>
    <slot />
  </main>
  
  <Footer lang={lang} />
</BaseLayout>
